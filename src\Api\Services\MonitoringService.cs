using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;

namespace Api.Services;

public class MonitoringService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<MonitoringService> _logger;

    public MonitoringService(AppDbContext dbContext, ILogger<MonitoringService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<ServiceResult<SystemOverviewResponse>> GetSystemOverviewAsync()
    {
        try
        {
            var workers = await _dbContext.Workers.ToListAsync();

            var latestMetrics = await _dbContext.WorkerMetrics.Where(m => workers.Select(w => w.Id).Contains(m.WorkerId)).GroupBy(m => m.WorkerId)
                .Select(g => g.OrderByDescending(m => m.RecordedAt).First()).ToListAsync();

            var activeAlerts = await _dbContext.WorkerAlerts.Where(a => !a.IsResolved).GroupBy(a => a.WorkerId)
                .Select(g => new { WorkerId = g.Key, Count = g.Count() }).ToListAsync();

            var totalWorkers = workers.Count;
            var onlineWorkers = workers.Count(w => w.Status != WorkerStatus.Offline);
            var offlineWorkers = totalWorkers - onlineWorkers;
            var healthyWorkers = workers.Count(w => w.HealthStatus == WorkerHealthStatus.Healthy);
            var warningWorkers = workers.Count(w => w.HealthStatus == WorkerHealthStatus.Warning);
            var criticalWorkers = workers.Count(w => w.HealthStatus == WorkerHealthStatus.Critical);
            var totalActiveTasks = latestMetrics.Sum(m => m.ActiveTasks);
            var totalActiveAlerts = activeAlerts.Sum(a => a.Count);
            var avgCpu = latestMetrics.Any() ? latestMetrics.Average(m => m.CpuUsagePercent) : 0;
            var avgMemory = latestMetrics.Any() ? latestMetrics.Average(m => m.MemoryUsagePercent) : 0;
            var avgDisk = latestMetrics.Any() ? latestMetrics.Average(m => m.DiskUsagePercent) : 0;

            var overview = new SystemOverviewResponse(totalWorkers, onlineWorkers, offlineWorkers, healthyWorkers, warningWorkers, criticalWorkers,
                totalActiveTasks, totalActiveAlerts, avgCpu, avgMemory, avgDisk);

            return ServiceResult<SystemOverviewResponse>.Success(overview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统概览时发生错误");
            return ServiceResult<SystemOverviewResponse>.Failure("获取系统概览失败");
        }
    }

    public async Task<ServiceResult<List<WorkerOverviewResponse>>> GetWorkersOverviewAsync()
    {
        try
        {
            var workers = await _dbContext.Workers.ToListAsync();

            var latestMetrics = await _dbContext.WorkerMetrics.Where(m => workers.Select(w => w.Id).Contains(m.WorkerId)).GroupBy(m => m.WorkerId)
                .Select(g => g.OrderByDescending(m => m.RecordedAt).First()).ToDictionaryAsync(m => m.WorkerId);

            var activeAlerts = await _dbContext.WorkerAlerts.Where(a => !a.IsResolved).GroupBy(a => a.WorkerId)
                .Select(g => new { WorkerId = g.Key, Count = g.Count() }).ToDictionaryAsync(x => x.WorkerId, x => x.Count);

            var overview = workers.Select(worker =>
            {
                latestMetrics.TryGetValue(worker.Id, out var metrics);
                activeAlerts.TryGetValue(worker.Id, out var alertCount);

                return new WorkerOverviewResponse(worker.Id, worker.Name, worker.Status, worker.HealthStatus, worker.LastActiveAt, metrics?.CpuUsagePercent,
                    metrics?.MemoryUsagePercent, metrics?.DiskUsagePercent, metrics?.ActiveTasks ?? 0, alertCount);
            }).ToList();

            return ServiceResult<List<WorkerOverviewResponse>>.Success(overview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作节点概览时发生错误");
            return ServiceResult<List<WorkerOverviewResponse>>.Failure("获取工作节点概览失败");
        }
    }

    public async Task<ServiceResult<WorkerHistoryResponse>> GetWorkerHistoryAsync(Guid workerId, DateTime startTime, DateTime endTime, string interval = "5m")
    {
        try
        {
            var metrics = await _dbContext.WorkerMetrics.Where(m => m.WorkerId == workerId && m.RecordedAt >= startTime && m.RecordedAt <= endTime)
                .OrderBy(m => m.RecordedAt).ToListAsync();

            var aggregatedData = AggregateMetrics(metrics, interval);

            var historyData = new WorkerHistoryResponse(workerId, startTime, endTime, aggregatedData);

            return ServiceResult<WorkerHistoryResponse>.Success(historyData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作节点 {WorkerId} 历史数据时发生错误", workerId);
            return ServiceResult<WorkerHistoryResponse>.Failure("获取历史数据失败");
        }
    }

    public async Task<ServiceResult<AlertSummaryResponse>> GetAlertSummaryAsync()
    {
        try
        {
            var alerts = await _dbContext.WorkerAlerts.Include(a => a.Worker).ToListAsync();

            var totalAlerts = alerts.Count;
            var unresolvedAlerts = alerts.Count(a => !a.IsResolved);
            var criticalAlerts = alerts.Count(a => a.AlertLevel == WorkerAlertLevel.Critical);
            var warningAlerts = alerts.Count(a => a.AlertLevel == WorkerAlertLevel.Warning);
            var infoAlerts = alerts.Count(a => a.AlertLevel == WorkerAlertLevel.Info);

            var recentAlerts = alerts.OrderByDescending(a => a.CreatedAt).Take(10).Select(a =>
                new WorkerAlertResponse(a.Id, a.WorkerId, a.Worker.Name, a.AlertType, a.AlertLevel, a.Title, a.Message, a.IsResolved, a.CreatedAt,
                    a.ResolvedAt)).ToList();

            var summary = new AlertSummaryResponse(totalAlerts, unresolvedAlerts, criticalAlerts, warningAlerts, infoAlerts, recentAlerts);

            return ServiceResult<AlertSummaryResponse>.Success(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取告警摘要时发生错误");
            return ServiceResult<AlertSummaryResponse>.Failure("获取告警摘要失败");
        }
    }

    public async Task<ServiceResult<List<WorkerAlertResponse>>> GetWorkerAlertsAsync(Guid workerId, int pageSize = 20, int pageNumber = 1)
    {
        try
        {
            var alerts = await _dbContext.WorkerAlerts.Where(a => a.WorkerId == workerId).Include(a => a.Worker).OrderByDescending(a => a.CreatedAt)
                .Skip((pageNumber - 1) * pageSize).Take(pageSize).Select(a => new WorkerAlertResponse(a.Id, a.WorkerId, a.Worker.Name, a.AlertType,
                    a.AlertLevel, a.Title, a.Message, a.IsResolved, a.CreatedAt, a.ResolvedAt)).ToListAsync();

            return ServiceResult<List<WorkerAlertResponse>>.Success(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作节点 {WorkerId} 告警时发生错误", workerId);
            return ServiceResult<List<WorkerAlertResponse>>.Failure("获取工作节点告警失败");
        }
    }

    public async Task<ServiceResult> ResolveAlertAsync(Guid alertId)
    {
        try
        {
            var alert = await _dbContext.WorkerAlerts.FirstOrDefaultAsync(a => a.Id == alertId);

            if (alert == null)
                return ServiceResult.Failure("告警不存在");

            if (alert.IsResolved)
                return ServiceResult.Failure("告警已经被解决");

            alert.IsResolved = true;
            alert.ResolvedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解决告警 {AlertId} 时发生错误", alertId);
            return ServiceResult.Failure("解决告警失败");
        }
    }

    private static List<MetricsDataPoint> AggregateMetrics(List<WorkerMetrics> metrics, string interval)
    {
        var intervalMinutes = interval switch
        {
            "1m" => 1,
            "5m" => 5,
            "15m" => 15,
            "1h" => 60,
            _ => 5
        };

        return metrics
            .GroupBy(m => new DateTime(m.RecordedAt.Year, m.RecordedAt.Month, m.RecordedAt.Day, m.RecordedAt.Hour,
                m.RecordedAt.Minute / intervalMinutes * intervalMinutes, 0)).Select(g => new MetricsDataPoint(g.Key, g.Average(m => m.CpuUsagePercent),
                g.Average(m => m.MemoryUsagePercent), g.Average(m => m.DiskUsagePercent), (int)g.Average(m => m.ActiveTasks))).OrderBy(d => d.Timestamp)
            .ToList();
    }

    // ==================== 新增方法 ====================

    public async Task<ServiceResult<List<WorkerMetricsResponse>>> GetWorkerMetricsAsync(Guid workerId, int hours = 24)
    {
        try
        {
            var startTime = DateTime.UtcNow.AddHours(-hours);

            var metrics = await _dbContext.WorkerMetrics.Where(m => m.WorkerId == workerId && m.RecordedAt >= startTime).OrderBy(m => m.RecordedAt).Select(m =>
                new WorkerMetricsResponse(m.Id, m.WorkerId, m.CpuUsagePercent, m.MemoryUsagePercent, m.DiskUsagePercent, m.NetworkReceivedGB, m.NetworkSentGB,
                    m.NetworkBandwidthMbps, m.ActiveConnections, m.ActiveTasks, m.RecordedAt)).ToListAsync();

            return ServiceResult<List<WorkerMetricsResponse>>.Success(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作节点 {WorkerId} 指标时发生错误", workerId);
            return ServiceResult<List<WorkerMetricsResponse>>.Failure("获取工作节点指标失败");
        }
    }
}