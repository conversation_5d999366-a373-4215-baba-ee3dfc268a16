using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AdminService(IConfiguration configuration)
{
    private readonly string _adminEmail = configuration["AdminAuth:Email"];
    private readonly string _adminPassword = configuration["AdminAuth:Password"];

    public async Task<ServiceResult<AdminLoginResponse>> LoginAsync(AdminLoginRequest request, HttpContext httpContext)
    {
        if (!string.Equals(request.Email, _adminEmail, StringComparison.OrdinalIgnoreCase) || request.Password != _adminPassword)
        {
            return ServiceResult<AdminLoginResponse>.Failure(new Error(ErrorType.Unauthorized, ErrorKeys.INVALID_CREDENTIALS, "管理员凭据无效。"));
        }

        var claims = new List<Claim>
        {
            new(ClaimTypes.Name, "admin"),
            new(ClaimTypes.Email, _adminEmail)
        };
        var claimsIdentity = new ClaimsIdentity(claims, "AdminScheme");
        var authProperties = new AuthenticationProperties
        {
            IsPersistent = true,
            ExpiresUtc = DateTimeOffset.UtcNow.AddHours(8)
        };

        await httpContext.SignInAsync("AdminScheme", new ClaimsPrincipal(claimsIdentity), authProperties);

        var response = new AdminLoginResponse(true, _adminEmail);
        return ServiceResult<AdminLoginResponse>.Success(response);
    }

    public async Task<ServiceResult> LogoutAsync(HttpContext httpContext)
    {
        await httpContext.SignOutAsync("AdminScheme");
        return ServiceResult.Success();
    }

    public Task<ServiceResult> GetDashboardDataAsync()
    {
        // This method is now only a placeholder as the authorization is handled by the endpoint policy.
        return Task.FromResult(ServiceResult.Success());
    }
}