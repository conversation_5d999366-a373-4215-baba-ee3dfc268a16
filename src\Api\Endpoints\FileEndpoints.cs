using Api.Extensions;
using Api.Services;
using Shared.DTOs;

namespace Api.Endpoints;

public static class FileEndpoints
{
    public static void MapFileEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/files").WithTags("Files");

        group.MapGet("/download/{taskId}", async Task<IResult> (Guid taskId, FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();

            var result = await fileDownloadService.GenerateDownloadUrlAsync(taskId, currentUser.UserId);
            return result.ToHttpResult();
        }).WithSummary("生成下载链接").WithDescription("为已完成的任务生成带签名的安全下载链接");

        group.MapPost("/download/link", async Task<IResult> (DownloadLinkRequest request, FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();

            var validFor = request.ValidFor ?? TimeSpan.FromHours(1);
            var result = await fileDownloadService.GenerateDownloadLinkAsync(request.TaskId, currentUser.UserId, validFor);
            return result.ToHttpResult();
        }).WithSummary("生成自定义下载链接").WithDescription("为任务生成自定义有效期的下载链接，支持1分钟到24小时");

        group.MapPost("/download/batch", async Task<IResult> (BatchDownloadRequest request, FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();

            var results = new List<BatchDownloadResult>();

            foreach (var taskId in request.TaskIds)
            {
                var result = await fileDownloadService.GenerateDownloadUrlAsync(taskId, currentUser.UserId);

                results.Add(new BatchDownloadResult(taskId, result.IsSuccess, result.Data, result.ErrorMessage));
            }

            var response = new BatchDownloadResponse(results, results.Count(r => r.Success), results.Count(r => !r.Success));

            return ResultExtensions.ApiOk(response);
        }).WithSummary("批量生成下载链接").WithDescription("为多个已完成的任务批量生成下载链接，最多支持50个任务");

        group.MapGet("/info/{taskId}", async Task<IResult> (Guid taskId, FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var result = await fileDownloadService.GetFileInfoAsync(taskId, currentUser.UserId);
            return result.ToHttpResult();
        }).WithSummary("获取文件信息").WithDescription("获取指定任务的详细文件信息，包括大小、类型、过期时间");

        group.MapGet("/stats", async Task<IResult> (FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();

            var result = await fileDownloadService.GetFileStatsAsync(currentUser.UserId);
            return result.ToHttpResult();
        }).WithSummary("获取文件统计").WithDescription("获取用户的文件下载统计信息，包括总数、大小、类型分布");

        group.MapGet("/history", async Task<IResult> (int page, int pageSize, FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();

            page = Math.Max(1, page);
            pageSize = Math.Clamp(pageSize, 1, 100);

            var result = await fileDownloadService.GetDownloadHistoryAsync(currentUser.UserId, page, pageSize);
            return result.ToHttpResult();
        }).WithSummary("获取下载历史").WithDescription("分页获取用户的文件下载历史记录");

        group.MapPost("/history/search", async Task<IResult> (DownloadHistoryRequest request, FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var result = await fileDownloadService.SearchDownloadHistoryAsync(currentUser.UserId, request);
            return result.ToHttpResult();
        }).WithSummary("搜索下载历史").WithDescription("根据条件搜索用户的下载历史，支持文件类型、时间范围筛选");

        group.MapPost("/cleanup", async Task<IResult> (FileCleanupRequest request, FileDownloadService fileDownloadService, HttpContext context) =>
        {
            var result = await fileDownloadService.CleanupFilesAsync(currentUser.UserId, request);
            return result.ToHttpResult();
        }).WithSummary("清理文件").WithDescription("清理用户的过期文件或失败任务文件，释放存储空间");
    }
}